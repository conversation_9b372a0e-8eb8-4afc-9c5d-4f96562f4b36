import Foundation
import Moya
import FirebaseAuth
import Alamofire
import os.log

// 网络状态枚举
enum NetworkStatus {
    case unknown
    case notReachable
    case reachableViaWiFi
    case reachableViaWWAN
    
    var description: String {
        switch self {
        case .unknown:
            return "网络状态未知"
        case .notReachable:
            return "网络不可用"
        case .reachableViaWiFi:
            return "使用WiFi网络"
        case .reachableViaWWAN:
            return "使用移动网络"
        }
    }
}

// 网络状态变化通知
extension Notification.Name {
    static let networkStatusDidChange = Notification.Name("networkStatusDidChange")
}

// 通用响应模型
struct APIResponse<T: Decodable>: Decodable {
    let status: Int
    let message: String
    let data: T?
    
    enum CodingKeys: String, CodingKey {
        case status
        case message
        case data
    }
}

// 空响应模型
struct EmptyResponse: Decodable {}

private let logger = Logger(subsystem: "com.looklock.app", category: "Network")

// 网络状态管理器
final class NetworkMonitor {
    static let shared = NetworkMonitor()
    
    private let reachabilityManager: NetworkReachabilityManager?
    private var currentNetworkStatus: NetworkStatus = .unknown
    
    var status: NetworkStatus {
        return currentNetworkStatus
    }
    
    var isReachable: Bool {
        return reachabilityManager?.isReachable ?? false
    }
    
    var isReachableViaWiFi: Bool {
        return reachabilityManager?.isReachableOnEthernetOrWiFi ?? false
    }
    
    var isReachableViaWWAN: Bool {
        return reachabilityManager?.isReachableOnCellular ?? false
    }
    
    private init() {
        reachabilityManager = NetworkReachabilityManager()
        startMonitoring()
    }
    
    deinit {
        stopMonitoring()
    }
    
    func startMonitoring() {
        reachabilityManager?.startListening { [weak self] status in
            guard let self = self else { return }
            
            let newStatus: NetworkStatus
            switch status {
            case .notReachable:
                newStatus = .notReachable
            case .reachable(.cellular):
                newStatus = .reachableViaWWAN
            case .reachable(.ethernetOrWiFi):
                newStatus = .reachableViaWiFi
            case .unknown:
                newStatus = .unknown
            }
            
            if self.currentNetworkStatus != newStatus {
                self.currentNetworkStatus = newStatus
                print("网络状态变化: \(newStatus.description)")
                NotificationCenter.default.post(
                    name: .networkStatusDidChange,
                    object: nil,
                    userInfo: ["status": newStatus]
                )
            }
        }
    }
    
    func stopMonitoring() {
        reachabilityManager?.stopListening()
    }
}

// 请求配置
struct NetworkConfig {
    static let timeoutInterval: TimeInterval = 30
    static let maxRetryCount = 3
    static let retryDelay: TimeInterval = 1
}

// 日志插件
final class NetworkLogger: PluginType {
    func willSend(_ request: RequestType, target: TargetType) {
        print("🌐 请求开始: \(target.path)")
        print("🌐 请求方法: \(target.method.rawValue)")
        print("🌐 请求头: \(target.headers?.description ?? "无")")
        
        // 打印请求参数
        switch target.task {
        case .requestParameters(let parameters, _):
            print("🌐 请求参数: \(parameters)")
        case .requestCompositeParameters(let bodyParameters, _, let urlParameters):
            print("🌐 请求体参数: \(bodyParameters)")
            print("🌐 URL 参数: \(urlParameters)")
        default:
            break
        }
    }

    func didReceive(_ result: Result<Response, MoyaError>, target: TargetType) {
        switch result {
        case .success(let response):
            print("✅ 请求成功: \(target.path)")
            print("✅ 状态码: \(response.statusCode)")
            if let data = try? JSONSerialization.jsonObject(with: response.data),
               let prettyData = try? JSONSerialization.data(withJSONObject: data, options: .prettyPrinted),
               let prettyString = String(data: prettyData, encoding: .utf8) {
                print("✅ 响应数据: \(prettyString)")
            } else {
                let raw = String(data: response.data, encoding: .utf8) ?? "无法解析的原始数据"
                print("✅ 原始响应: \(raw)")
            }
        case .failure(let error):
            print("❌ 请求失败: \(target.path)")
            print("❌ 错误信息: \(error.localizedDescription)")
        }
    }
}

struct CommonParameters {
    static let client = "ios"  // 客户端类型
    static let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? "0000000000"  // 设备ID
    static let av = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"  // 版本号
    static let timezone = TimeZone.current.identifier  // 时区
    static var lang: String {
        // 使用LocalizationManager的当前语言设置
        return LocalizationManager.shared.currentLanguage.rawValue
    }
    static let countryCode = Locale.current.region?.identifier ?? "US"
    static var parameters: [String: Any] {
        return [
            "client": client,
            "deviceId": deviceId,
            "av": av,
            "t": Int(Date().timeIntervalSince1970),  // 时间戳
            "timezone": timezone,
            "lang": lang,
            "country_code": countryCode
        ]
    }
}

enum APIService {
    case login(email: String,pwd: String)
    case googleAuthLogin(token: String)
    case appleTokenLogin(loginData: UserAppleTokenLogin)
    case thirdPartyLogin(credential: ThirdPartyLoginCredential)
    case getQiniuUploadToken(permanent: Bool)
    case updateUserInfo(username: String, avatar: String?)
    case updatePushToken(token: String)
    case addFriend(shareCode: String)
    case deleteFriend(friendId: String)
    case handleFriendRequest(action: Int, requestId: Int)
    case getFriendList
    case getFriendRequestList
    case getLockScreenHistory(page: Int, pageSize: Int)
    case getUserLockScreenInfo
    case syncLockScreen(lockScreenInfo: String, toFriends: [String],isAgain: Bool)
    case deregister
    case getUserInfo
    case lockScreenSuccess(pid: String)
    case resetPassword(email: String)
    case markHistoryRead(id: Int)
    case getVersionInfo
    case getUserInfoByCode(shareCode: String)
}

extension APIService: TargetType {
    var baseURL: URL { EnvManager.current.baseURL }
    
    var path: String {
        switch self {
        case .login:
            return "/api/v1/user/login"
        case .getQiniuUploadToken:
            return "/api/v1/upload/token"
        case .updateUserInfo:
            return "/api/v1/user/update/userinfo"
        case .updatePushToken:
            return "/api/v1/user/update/pushtoken"
        case .addFriend:
            return "/api/v1/friend/add_request"
        case .deleteFriend:
            return "/api/v1/friend/delete"
        case .handleFriendRequest:
            return "/api/v1/friend/handle_request"
        case .getFriendList:
            return "/api/v1/friend/list"
        case .getFriendRequestList:
            return "/api/v1/friend/request_list"
        case .getLockScreenHistory:
            return "/api/v1/user/lockscreen/history"
        case .getUserLockScreenInfo:
            return "/api/v1/user/lockscreen/info"
        case .syncLockScreen:
            return "/api/v1/friend/sync_lock_screen"
        case .deregister:
            return "/api/v1/user/deregister"
        case .getUserInfo:
            return "/api/v1/user/info"
        case .lockScreenSuccess:
            return "/api/v1/user/lockscreen/success"
        case .googleAuthLogin:
            return "/api/v1/user/token_login"
        case .appleTokenLogin:
            return "/api/v1/user/apple_token_login"
        case .thirdPartyLogin:
            return "/api/v1/user/token_login"
        case .resetPassword:
            return "/api/v1/user/email/resetpasswd/code"
        case .markHistoryRead:
            return "/api/v1/user/readed/history"
        case .getVersionInfo:
            return "/api/version"
        case .getUserInfoByCode:
            return "/api/v1/user/infobycode"
        }
    }
    
    var method: Moya.Method {
        switch self {
        case .login, .updateUserInfo, .updatePushToken, .addFriend, .deleteFriend, .handleFriendRequest, .getLockScreenHistory, .syncLockScreen:
            return .post
        case .getQiniuUploadToken, .getFriendList, .getFriendRequestList, .getUserLockScreenInfo, .deregister, .getUserInfo:
            return .get
        case .lockScreenSuccess:
            return .post
        case .googleAuthLogin, .resetPassword, .thirdPartyLogin:
            return .post
        case .markHistoryRead:
            return .post
        case .getVersionInfo:
            return .get
        case .getUserInfoByCode:
            return .post
        case .appleTokenLogin:
            return .post
        }
    }
    
    var task: Task {
        var commonParams = CommonParameters.parameters
        
        switch self {
        case let .login(email,pwd):
            let countryCode = Locale.current.region?.identifier ?? "US"
            let body = [
                "country_code": countryCode,
                "auth_email": email,
                "passwd": pwd
            ]
            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)

        case let .getQiniuUploadToken(permanent):
            commonParams["permanent"] = permanent
            return .requestParameters(
                parameters: commonParams,
                encoding: URLEncoding.queryString
            )
        case let .updateUserInfo(username, avatar):
            var body: [String: Any] = ["nickname": username]
            if let avatar = avatar {
                body["avatar"] = avatar
            }
            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)

        case let .updatePushToken(token):
            let body = ["push_token": token]
            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)

        case let .addFriend(shareCode):
            let body = ["share_code": shareCode]
            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)

        case let .deleteFriend(friendId):
            let body = ["friend_id": friendId]
            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)

        case let .handleFriendRequest(action, requestId):
            let body = ["action": action, "request_id": requestId]
            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)

        case .getFriendList, .getFriendRequestList, .getUserLockScreenInfo, .getUserInfo:
            return .requestParameters(parameters: commonParams, encoding: URLEncoding.queryString)

        case let .getLockScreenHistory(page, pageSize):
            let body: [String: Any] = [
                "page": page,
                "page_size": pageSize
            ]
            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)

        case let .syncLockScreen(lockScreenInfo, toFriends, isAgain):
            let body: [String: Any] = [
                "img": lockScreenInfo,
                "to_friends": toFriends,
                "is_again": isAgain
            ]
            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)
        case .deregister:
            return .requestParameters(parameters: commonParams, encoding: URLEncoding.default)
        case let .lockScreenSuccess(pid):
            let body: [String: Any] = ["sync_screen_id": pid]
            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)
        case .googleAuthLogin(token: let token):
            let countryCode = Locale.current.region?.identifier ?? "US"
            let body = [
                "country_code": countryCode,
                "platform": "google",
                "token": token
            ]
            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)

        case .appleTokenLogin(loginData: let loginData):
            let countryCode = Locale.current.region?.identifier ?? "US"
            var body: [String: Any] = [
                "country_code": countryCode,
                "token": loginData.identityToken,
                "platform": "apple"
            ]

            if let fullName = loginData.fullName?.formattedDisplayName() {
                body["nickname"] = fullName
            }
            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)

        case .thirdPartyLogin(credential: let credential):
            let countryCode = Locale.current.region?.identifier ?? "US"
            var body: [String: Any] = [
                "country_code": countryCode,
                "platform": credential.type.rawValue,
                "token": credential.token
            ]

            // 添加用户信息（如果有）
            if let userInfo = credential.userInfo {
                if let name = userInfo.name {
                    body["nickname"] = name
                }
                if let avatar = userInfo.avatar {
                    body["avatar"] = avatar
                }
            }

            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)
        case let .resetPassword(email):
            let body = ["email": email]
            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)
        case let .markHistoryRead(id):
            let body: [String: Any] = ["id": id]
            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)
        case .getVersionInfo:
            return .requestParameters(parameters: commonParams, encoding: URLEncoding.queryString)
        case let .getUserInfoByCode(shareCode):
            let body = ["share_code": shareCode]
            return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: commonParams)
        }
    }
    
    var headers: [String: String]? {
        var headers: [String: String] = [
            "Content-Type": "application/json",
            "Accept": "application/json"
        ]
        
        // 除了登录接口外，其他接口都需要带上 token
        if case .login = self {
            return headers
        }
        
        if case .resetPassword = self {
            return headers // 找回密码接口无需token
        }
        
        if let token = UserService.shared.getAuthToken() {
            headers["Authorization"] = "Bearer \(token)"
        }
        
        return headers
    }
    
    // 尝试获取 Firebase Auth 缓存的 token（同步操作）
    private func getCachedFirebaseToken() -> String? {
        var token: String?
        let semaphore = DispatchSemaphore(value: 0)
        
        // 使用 forceRefresh: false 参数，这会优先返回缓存的 token，是同步操作
        Auth.auth().currentUser?.getIDTokenForcingRefresh(false) { idToken, error in
            token = idToken
            semaphore.signal()
        }
        
        // 设置一个短暂的等待时间，避免长时间阻塞
        let _ = semaphore.wait(timeout: .now() + 0.5)
        return token
    }
    
    var sampleData: Data {
        return Data()
    }
    
    var validate: Bool {
        return true
    }
}

class NetworkManager {
    static let shared = NetworkManager()
    private let provider: MoyaProvider<APIService>
    private let networkMonitor = NetworkMonitor.shared
    
    // 请求节流控制
    private var lastRequestTimes: [String: Date] = [:]
    private let requestThrottleInterval: TimeInterval = 1.0 // 1秒内相同请求不重复发送
    private let throttleQueue = DispatchQueue(label: "com.looklock.networkmanager.throttle")
    
    private init() {
        // 配置 MoyaProvider
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = NetworkConfig.timeoutInterval
        configuration.timeoutIntervalForResource = NetworkConfig.timeoutInterval
        
        let logger = NetworkLogger()
        
        provider = MoyaProvider<APIService>(
            session: Session(configuration: configuration),
            plugins: [logger]
        )
    }
    
    // 检查是否应该节流请求
    private func shouldThrottleRequest(_ key: String) -> Bool {
        return throttleQueue.sync {
            if let lastTime = lastRequestTimes[key] {
                let timeSinceLastRequest = Date().timeIntervalSince(lastTime)
                if timeSinceLastRequest < requestThrottleInterval {
                    print("⚠️ 请求被节流: \(key), 间隔: \(timeSinceLastRequest)s")
                    return true
                }
            }
            
            // 更新最后请求时间
            lastRequestTimes[key] = Date()
            return false
        }
    }
    
    // MARK: - 通用请求方法
    private func request<T: Decodable>(_ target: APIService, model: T.Type = T.self, retryCount: Int = 0, completion: @escaping (Result<T, Error>) -> Void) {
        // 生成请求唯一标识符 (路径+参数)
        let requestKey = "\(target.path)-\(target.method.rawValue)"
        
        // 检查是否应该节流请求
        if shouldThrottleRequest(requestKey) {
            completion(.failure(NetworkError.throttle))
            return
        }
        
        // 检查网络状态
        guard networkMonitor.isReachable else {
            completion(.failure(NetworkError.networkError(underlying: nil)))
            return
        }

        provider.request(target) { result in
            switch result {
            case .success(let response):
                do {
                    // 状态码 401
                    if response.statusCode == 401 {
                        completion(.failure(NetworkError.unauthorized))
                        return
                    }
                    
                    let apiResponse = try JSONDecoder().decode(APIResponse<T>.self, from: response.data)
                    
                    // API 状态判断
                    guard apiResponse.status == 200 else {
                        completion(.failure(NetworkError.serverError(code: apiResponse.status, message: apiResponse.message)))
                        return
                    }
                    
                    guard let data = apiResponse.data else {
                        completion(.failure(NetworkError.custom(apiResponse.message)))
                        return
                    }
                    
                    completion(.success(data))

                } catch let decodingError as DecodingError {
                    // 解码错误，打印详细信息并直接失败，不进行重试
                    print("🔴 解码错误，不进行重试: \(decodingError)")
                    
                    // 尝试打印原始JSON以帮助调试
                    if let rawJSON = String(data: response.data, encoding: .utf8) {
                        print("🔴 原始JSON数据: \(rawJSON)")
                    }
                    
                    completion(.failure(NetworkError.parseError(underlying: decodingError)))
                } catch {
                    // 其他类型错误才进行重试
                    print("⚠️ 非解码错误，尝试重试: \(error)")
                    self.retryOrFail(error: error, currentRetry: retryCount, target: target, completion: completion)
                }
                
            case .failure(let error):
                // 网络请求失败重试
                self.retryOrFail(error: error, currentRetry: retryCount, target: target, completion: completion)
            }
        }
    }

    private func retryOrFail<T: Decodable>(
        error: Error,
        currentRetry: Int,
        target: APIService,
        completion: @escaping (Result<T, Error>) -> Void
    ) {
        if currentRetry < NetworkConfig.maxRetryCount {
            let nextRetry = currentRetry + 1
            DispatchQueue.global().asyncAfter(deadline: .now() + NetworkConfig.retryDelay) {
                self.request(target, retryCount: nextRetry, completion: completion)
            }
        } else {
            completion(.failure(error))
        }
    }

    
    // MARK: - 无数据响应的请求方法
    private func requestVoid(_ target: APIService, completion: @escaping (Result<Void, NetworkError>) -> Void) {
        provider.request(target) { result in
            switch result {
            case let .success(response):
                do {
                    let apiResponse = try JSONDecoder().decode(APIResponse<EmptyResponse>.self, from: response.data)
                    
                    // 检查 API 状态
                    if apiResponse.status != 200 {
                        completion(.failure(NetworkError.serverError(code: apiResponse.status, message: apiResponse.message)))
                        return
                    }
                    
                    completion(.success(()))
                } catch {
                    completion(.failure(NetworkError.parseError(underlying: error)))
                }
            case let .failure(error):
                completion(.failure(NetworkError.networkError(underlying: error)))
            }
        }
    }
    
    // MARK: - 用户相关
    func login(token: String, completion: @escaping (Result<LoginResponse, Error>) -> Void) {
        request(.googleAuthLogin(token: token),model: LoginResponse.self) { result in
            switch result {
            case .success(let response):
                // 保存 token
                if let token = response.token {
                    UserService.shared.saveAuthToken(token)
                }
                if let userInfo = response.userInfo {
                    UserService.shared.updateLocalUserProfile(userInfo)
                }
                completion(.success(response))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    func getUserInfo(completion: @escaping (Result<UserProfile, Error>) -> Void) {
        request(.getUserInfo, model: UserProfile.self) { result in
            switch result {
            case .success(let userProfile):
                // 更新本地用户信息
                UserService.shared.updateLocalUserProfile(userProfile)
                completion(.success(userProfile))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }

    /// 服务器邮箱登录（无需 Firebase）
    func emailLogin(email: String, password: String, completion: @escaping (Result<LoginResponse, Error>) -> Void) {
        request(.login(email: email, pwd: password), model: LoginResponse.self) { result in
            switch result {
            case .success(let response):
                if let token = response.token {
                    UserService.shared.saveAuthToken(token)
                }
                if let userInfo = response.userInfo {
                    UserService.shared.updateLocalUserProfile(userInfo)
                }
                completion(.success(response))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }

    /// Apple Token 登录
    func appleTokenLogin(loginData: UserAppleTokenLogin, completion: @escaping (Result<LoginResponse, Error>) -> Void) {
        request(.appleTokenLogin(loginData: loginData), model: LoginResponse.self) { result in
            switch result {
            case .success(let response):
                // 保存认证 token
                if let token = response.token {
                    UserService.shared.saveAuthToken(token)
                }

                // 更新用户信息
                if let userInfo = response.userInfo {
                    UserService.shared.updateLocalUserProfile(userInfo)
                }

                // 保存 EAS 文件路径
                if let easFilePath = response.easfilePath {
                    UserDefaults.standard.set(easFilePath, forKey: AppUserDefaultsKeys.easFileUrl)
                }

                completion(.success(response))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }

    /// 第三方登录
    func thirdPartyLogin(credential: ThirdPartyLoginCredential, completion: @escaping (Result<LoginResponse, Error>) -> Void) {
        request(.thirdPartyLogin(credential: credential), model: LoginResponse.self) { result in
            switch result {
            case .success(let response):
                if let token = response.token {
                    UserService.shared.saveAuthToken(token)
                }
                if let userInfo = response.userInfo {
                    UserService.shared.updateLocalUserProfile(userInfo)
                }
                completion(.success(response))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }

    // MARK: - 好友相关
    func addFriend(shareCode: String, completion: @escaping (Result<Void, NetworkError>) -> Void) {
        requestVoid(.addFriend(shareCode: shareCode), completion: completion)
    }
    
    func deleteFriend(friendId: String, completion: @escaping (Result<Void, NetworkError>) -> Void) {
        requestVoid(.deleteFriend(friendId: friendId), completion: completion)
    }
    
    func handleFriendRequest(action: Int, requestId: Int, completion: @escaping (Result<Void, NetworkError>) -> Void) {
        requestVoid(.handleFriendRequest(action: action, requestId: requestId), completion: completion)
    }
    
    func getFriendList(completion: @escaping (Result<[FriendItem], Error>) -> Void) {
        request(.getFriendList,model: [FriendItem].self) { result in
            switch result {
            case .success(let response):
                completion(.success(response))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    func getFriendRequestList(completion: @escaping (Result<[FriendRequestItem], Error>) -> Void) {
        request(.getFriendRequestList,model: [FriendRequestItem].self) { result in
            switch result {
            case .success(let response):
                completion(.success(response))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    // MARK: - 七牛云相关
    func getQiniuUploadToken(permanent: Bool = false,
                             completion: @escaping (Result<QiniuTokenResponse, Error>) -> Void) {
        request(.getQiniuUploadToken(permanent: permanent), completion: completion)
    }
    
    // MARK: - 用户锁屏信息
    func fetchUserLockScreenInfo(completion: @escaping (Result<UserLockScreenInfo, Error>) -> Void) {
        request(.getUserLockScreenInfo, model: UserLockScreenInfo.self, completion: completion)
    }
    
    // MARK: - Firebase 相关
    func firebaseLogin(email: String, password: String, completion: @escaping (Result<LoginResponse, Error>) -> Void) {
        Auth.auth().signIn(withEmail: email, password: password) { [weak self] result, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let user = result?.user else {
                completion(.failure(NetworkError.custom("User Login failed")))
                return
            }
            
            // 获取 Firebase token
            user.getIDToken { token, error in
                if let error = error {
                    completion(.failure(error))
                    return
                }
                
                guard let token = token else {
                    completion(.failure(NetworkError.custom("Token is nil")))
                    return
                }
                

                
                // 调用登录接口
                self?.login(token: token) { result in
                    switch result {
                    case .success(let info):
                        completion(.success(info))
                    case .failure(let error):
                        completion(.failure(error))
                    }
                }
            }
        }
    }
    
    func firebaseRegister(email: String, password: String, completion: @escaping (Result<User, Error>) -> Void) {
        Auth.auth().createUser(withEmail: email, password: password) { [weak self] result, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let user = result?.user else {
                completion(.failure(NetworkError.custom("User creation failed")))
                return
            }
            
            // 获取 Firebase token
            user.getIDToken { token, error in
                if let error = error {
                    completion(.failure(error))
                    return
                }
                
                guard let token = token else {
                    completion(.failure(NetworkError.custom("Token is nil")))
                    return
                }
                

                
                // 调用登录接口
                self?.login(token: token) { result in
                    switch result {
                    case .success:
                        completion(.success(user))
                    case .failure(let error):
                        completion(.failure(error))
                    }
                }
            }
        }
    }
    
    func firebaseLogout(completion: @escaping (Result<Void, Error>) -> Void) {
        do {
            try Auth.auth().signOut()
            completion(.success(()))
        } catch {
            completion(.failure(error))
        }
    }
    
    func getCurrentUser() -> User? {
        return Auth.auth().currentUser
    }
    
    // MARK: - 用户信息更新
    func updateUserProfile(username: String, avatar: UIImage?, completion: @escaping (Result<String?, Error>) -> Void) {
        // 如果有头像，先上传到七牛云
        if let avatar = avatar {
            AvatarUploaderService.shared.uploadAvatar(avatar,
                progress: { progress in
                    // 更新上传进度
                    print("Upload progress: \(progress)")
                },
                completion: { [weak self]  result in
                    switch result {
                    case .success(let url):
                        
                        self?.updateUserInfo(username: username, avatar: url) { userInfoResult in
                            switch userInfoResult {
                            case .success:
                                completion(.success(url)) // Success, return the new URL
                            case .failure(let error):
                                completion(.failure(error))
                            }
                        }
                        print("Upload success: \(url)")
                    case .failure(let error):
                        // 处理错误
                        print("Upload failed: \(error.localizedDescription)")
                        completion(.failure(error))
                    }
                }
            )
        } else {
            // 没有头像，直接更新用户信息
            updateUserInfo(username: username, avatar: nil) { result in
                switch result {
                case .success:
                    completion(.success(nil)) // Success, return nil as no new URL
                case .failure(let error):
                    completion(.failure(error))
                }
            }
        }
    }
    
    private func updateUserInfo(username: String, avatar: String?, completion: @escaping (Result<Void, NetworkError>) -> Void) {
        requestVoid(.updateUserInfo(username: username, avatar: avatar), completion: completion)
    }
    
    // MARK: - 推送相关
    func updatePushToken(_ token: String) async throws {
        // 当 token 为空字符串时，视为"解绑推送"操作，仍然向服务器发送请求以清除绑定。
        // 若需要进一步校验，可在服务器端拒绝无效 token。
        // 此处不再抛错，以避免登出流程无法继续。
        
        // 检查网络状态
        guard networkMonitor.isReachable else {
            throw NetworkError.networkError(underlying: nil)
        }
        
        // 执行请求
        return try await withCheckedThrowingContinuation { continuation in
            requestVoid(.updatePushToken(token: token)) { result in
                switch result {
                case .success:
                    continuation.resume()
                case .failure(let error):
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    // MARK: - LockScreen History
    func getLockScreenHistory(page: Int = 1, pageSize: Int = 20, completion: @escaping (Result<LockScreenHistoryResponse, Error>) -> Void) {
        // 生成请求的唯一标识，包含分页信息
        let requestKey = "getLockScreenHistory-page\(page)-size\(pageSize)"
        
        // 检查是否应该节流请求
        if shouldThrottleRequest(requestKey) {
            completion(.failure(NetworkError.throttle))
            return
        }
        
        provider.request(.getLockScreenHistory(page: page, pageSize: pageSize)) { result in
            switch result {
            case .success(let response):
                do {
                    // 状态码 401
                    if response.statusCode == 401 {
                        completion(.failure(NetworkError.unauthorized))
                        return
                    }
                    
                    // 解析顶层响应
                    let apiResponse = try JSONDecoder().decode(APIResponse<LockScreenHistoryResponse>.self, from: response.data)
                    
                    // API 状态判断
                    guard apiResponse.status == 200 else {
                        completion(.failure(NetworkError.serverError(code: apiResponse.status, message: apiResponse.message)))
                        return
                    }
                    
                    guard let data = apiResponse.data else {
                        completion(.failure(NetworkError.parseError(underlying: nil)))
                        return
                    }
                    
                    // 检查是否有全局红点状态
                    if page == 1 && data.showRed != nil {
                        // 始终从 API 获取红点状态，仅在第一页时更新，避免分页加载干扰
                        DispatchQueue.main.async {
                            // 发送通知更新红点状态
                            NotificationCenter.default.post(
                                name: Notification.Name("LockScreenHistoryRedDotUpdated"),
                                object: nil,
                                userInfo: ["showRed": data.showRed ?? false]
                            )
                            
                            // 如果没有未读消息，清除应用角标
                            if data.showRed == false {
                                UIApplication.shared.applicationIconBadgeNumber = 0
                            }
                        }
                    }
                    
                    // 直接使用响应对象（不再需要转换）
                    completion(.success(data))
                    
                } catch let decodingError as DecodingError {
                    // 解码错误，打印详细信息并直接失败，不进行重试
                    print("🔴 解码错误，不进行重试: \(decodingError)")
                    
                    // 尝试打印原始JSON以帮助调试
                    if let rawJSON = String(data: response.data, encoding: .utf8) {
                        print("🔴 原始JSON数据: \(rawJSON)")
                    }
                    
                    completion(.failure(NetworkError.parseError(underlying: decodingError)))
                } catch {
                    // 其他类型错误才进行重试
                    print("⚠️ 非解码错误，尝试重试: \(error)")
                    self.retryOrFail(error: error, currentRetry: 0, target: .getLockScreenHistory(page: page, pageSize: pageSize), completion: completion)
                }
                
            case .failure(let error):
                // 网络请求失败重试
                self.retryOrFail(error: error, currentRetry: 0, target: .getLockScreenHistory(page: page, pageSize: pageSize), completion: completion)
            }
        }
    }
    
    func syncLockScreen(lockScreenInfo: String, toFriends: [String], isAgain: Bool, completion: @escaping (Result<Void, NetworkError>) -> Void) {
        requestVoid(.syncLockScreen(lockScreenInfo: lockScreenInfo, toFriends: toFriends, isAgain: isAgain), completion: completion)
    }
    
    // 根据邀请码获取用户信息
    func getUserInfoByCode(shareCode: String, completion: @escaping (Result<UserProfile, Error>) -> Void) {
        request(.getUserInfoByCode(shareCode: shareCode), model: UserProfile.self, completion: completion)
    }

    func deregisterUser() async throws {
        return try await withCheckedThrowingContinuation { continuation in
            requestVoid(.deregister) { result in
                // 将 throws 移到内部处理
                switch result {
                case .success:
                    continuation.resume()
                case .failure(let error):
                    continuation.resume(throwing: error)
                }
            }
        }
    }

    // MARK: - Lock Screen Operations
    func confirmLockScreenSuccess(pid: String, completion: @escaping (Result<Void, NetworkError>) -> Void) {
        requestVoid(.lockScreenSuccess(pid: pid), completion: completion)
    }

    // 找回密码（Native 邮箱账号）
    func resetPassword(email: String, completion: @escaping (Result<Void, NetworkError>) -> Void) {
        requestVoid(.resetPassword(email: email), completion: completion)
    }

    // 标记历史项为已读，并返回更新后的全局红点状态
    func readLockScreenHistory(id: Int, completion: @escaping (Result<ReadHistoryResponse?, Error>) -> Void) {
        provider.request(.markHistoryRead(id: id)) { result in
            switch result {
            case .success(let response):
                do {
                    // 解析新的响应格式，包含全局红点状态
                    let apiResponse = try JSONDecoder().decode(APIResponse<ReadHistoryResponse>.self, from: response.data)
                    if apiResponse.status == 200 {
                        // 如果已读操作成功且返回没有更多未读消息，清除应用角标
                        if let data = apiResponse.data, data.hasUnread == false {
                            DispatchQueue.main.async {
                                UIApplication.shared.applicationIconBadgeNumber = 0
                            }
                        }
                        completion(.success(apiResponse.data))
                    } else {
                        completion(.failure(NetworkError.serverError(code: apiResponse.status, message: apiResponse.message)))
                    }
                } catch {
                    // 兼容旧版本API响应，如果解析失败，按成功处理，但不返回红点状态
                    print("解析readLockScreenHistory响应失败，可能是旧版本API: \(error)")
                    completion(.success(nil))
                }
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    


    func fetchVersionInfo(completion: @escaping (Result<VersionInfo, Error>) -> Void) {
        provider.request(.getVersionInfo) { result in
            switch result {
            case .success(let response):
                do {
                    let apiResponse = try JSONDecoder().decode(APIResponse<VersionInfo>.self, from: response.data)
                    if apiResponse.status == 0, let data = apiResponse.data {
                        completion(.success(data))
                    } else {
                        completion(.failure(NetworkError.serverError(code: apiResponse.status, message: apiResponse.message)))
                    }
                } catch {
                    completion(.failure(error))
                }
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
} 
