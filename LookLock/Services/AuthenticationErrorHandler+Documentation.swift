import Foundation

/**
 # 认证错误处理器文档
 
 ## 概述
 `AuthenticationErrorHandler` 是一个专门处理 API 错误代码 10001 的单例类，用于处理用户会话过期和认证失败的情况。
 
 ## 功能特性
 
 ### 1. 错误代码 10001 检测
 - 自动检测 API 响应中的错误代码 10001
 - 仅对错误代码 10001 进行特殊处理，其他错误正常返回
 
 ### 2. 本地化错误提示
 - 使用现有的本地化键显示用户友好的错误消息
 - 支持中文简体、中文繁体、英文、日文、韩文
 - 错误标题：`alert_title_error`
 - 错误消息：`error_unauthorized`
 
 ### 3. 用户会话清理
 - 自动清除所有用户相关数据
 - 清除 UserDefaults 中的用户配置
 - 清除 Keychain 中的认证令牌
 - 清除好友列表和历史记录缓存
 - 清除图片缓存
 - 发送用户登出通知
 
 ### 4. 自动导航到登录页面
 - 显示错误提示后自动导航到登录页面
 - 使用平滑的过渡动画
 - 支持多种导航方式的备用方案
 
 ## 使用方式
 
 ### 在 NetworkManager 中的集成
 ```swift
 // 检查是否是认证错误代码 10001
 if apiResponse.status == 10001 {
     AuthenticationErrorHandler.shared.handleAuthenticationError(
         errorCode: apiResponse.status,
         errorMessage: apiResponse.message
     )
 }
 ```
 
 ### 手动调用（如需要）
 ```swift
 AuthenticationErrorHandler.shared.handleAuthenticationError(
     errorCode: 10001,
     errorMessage: "Session expired"
 ) {
     // 可选的完成回调
     print("认证错误处理完成")
 }
 ```
 
 ## 处理流程
 
 1. **错误检测**: 检查错误代码是否为 10001
 2. **显示提示**: 在主线程显示本地化的错误提示弹窗
 3. **用户确认**: 用户点击"确定"按钮
 4. **清理会话**: 异步清除所有用户会话数据
 5. **导航登录**: 导航到登录页面
 6. **完成回调**: 执行可选的完成回调
 
 ## 线程安全
 
 - 所有 UI 操作都确保在主线程执行
 - 数据清理操作在后台线程异步执行
 - 使用 weak self 避免循环引用
 
 ## 错误处理
 
 - 如果无法获取顶层视图控制器，会延迟重试
 - 如果数据清理失败，仍会继续导航到登录页面
 - 提供多种导航方式的备用方案
 
 ## 本地化支持
 
 支持的语言和对应的错误消息：
 
 - **中文简体**: "登录已过期，请重新登录"
 - **中文繁体**: "登入已過期，請重新登入"
 - **English**: "Session expired, please login again"
 - **日本語**: "セッションが期限切れです。再度ログインしてください"
 - **한국어**: "세션이 만료되었습니다. 다시 로그인해주세요"
 
 ## 注意事项
 
 1. 该处理器仅处理错误代码 10001，其他错误代码会被忽略
 2. 会话清理是不可逆的操作，请确保只在真正的认证失败时调用
 3. 导航到登录页面会替换整个应用的根视图控制器
 4. 所有操作都是异步的，不会阻塞当前线程
 
 ## 测试建议
 
 可以通过以下方式测试该功能：
 
 1. 模拟 API 返回错误代码 10001
 2. 验证错误提示是否正确显示
 3. 验证用户数据是否被正确清理
 4. 验证是否正确导航到登录页面
 5. 验证多语言支持是否正常工作
 */

// MARK: - 测试辅助方法
extension AuthenticationErrorHandler {
    
    /// 仅用于测试的方法 - 模拟错误代码 10001
    /// - Warning: 仅在调试模式下可用，不要在生产环境中使用
    #if DEBUG
    func simulateAuthenticationError() {
        print("🧪 [测试] 模拟认证错误 10001")
        handleAuthenticationError(
            errorCode: 10001,
            errorMessage: "Test session expired"
        ) {
            print("🧪 [测试] 认证错误处理完成")
        }
    }
    #endif
}
