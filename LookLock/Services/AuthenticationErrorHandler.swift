import Foundation
import UIKit

/// 认证错误处理器 - 专门处理认证失败和会话过期的情况
class AuthenticationErrorHandler {
    static let shared = AuthenticationErrorHandler()
    
    private init() {}
    
    /// 处理认证错误，特别是错误代码 10001
    /// - Parameters:
    ///   - errorCode: API 返回的错误代码
    ///   - errorMessage: API 返回的错误消息
    ///   - completion: 处理完成后的回调
    func handleAuthenticationError(errorCode: Int, errorMessage: String, completion: (() -> Void)? = nil) {
        // 检查是否是认证失败错误代码 10001
        guard errorCode == 10001 else {
            completion?()
            return
        }
        
        print("🔐 检测到认证错误 10001，开始处理会话过期...")
        
        // 在主线程执行 UI 相关操作
        DispatchQueue.main.async { [weak self] in
            self?.handleSessionExpired(errorMessage: errorMessage, completion: completion)
        }
    }
    
    /// 处理会话过期的情况
    private func handleSessionExpired(errorMessage: String, completion: (() -> Void)?) {
        // 1. 显示本地化的错误提示
        showSessionExpiredAlert(errorMessage: errorMessage) { [weak self] in
            // 2. 清除用户会话数据
            Task {
                await self?.clearUserSession()
                
                // 3. 导航到登录页面
                DispatchQueue.main.async {
                    self?.navigateToLogin()
                    completion?()
                }
            }
        }
    }
    
    /// 显示会话过期的本地化提示
    private func showSessionExpiredAlert(errorMessage: String, completion: @escaping () -> Void) {
        // 使用现有的本地化键来构建用户友好的错误消息
        let title = "alert_title_error".localized
        let message = "error_unauthorized".localized

        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)

        let okAction = UIAlertAction(title: "ok".localized, style: .default) { _ in
            completion()
        }
        alert.addAction(okAction)

        // 获取当前最顶层的视图控制器来显示弹窗
        if let topViewController = UIApplication.shared.topViewController {
            // 确保在主线程显示弹窗
            if Thread.isMainThread {
                topViewController.present(alert, animated: true)
            } else {
                DispatchQueue.main.async {
                    topViewController.present(alert, animated: true)
                }
            }
        } else {
            // 如果无法获取顶层控制器，延迟一下再试
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                if let topViewController = UIApplication.shared.topViewController {
                    topViewController.present(alert, animated: true)
                } else {
                    // 仍然无法获取，直接执行清理和导航
                    completion()
                }
            }
        }
    }
    
    /// 清除用户会话数据
    private func clearUserSession() async {
        do {
            print("🔐 开始清除用户会话数据...")
            
            // 使用 UserService 的现有方法清除所有用户数据
            try await UserService.shared.clearUserData()
            
            // 发送用户登出通知
            NotificationCenter.default.post(name: NotificationName.userDidLogout, object: nil)
            
            print("🔐 用户会话数据清除完成")
        } catch {
            print("🔐 清除用户会话数据时发生错误: \(error.localizedDescription)")
            // 即使清除失败，也要继续导航到登录页面
        }
    }
    
    /// 导航到登录页面
    private func navigateToLogin() {
        print("🔐 导航到登录页面...")

        // 确保在主线程执行 UI 操作
        guard Thread.isMainThread else {
            DispatchQueue.main.async { [weak self] in
                self?.navigateToLogin()
            }
            return
        }

        // 创建登录页面
        let loginVC = IntroViewController()
        let navController = UINavigationController(rootViewController: loginVC)

        // 获取当前窗口并切换根视图控制器
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {

            UIView.transition(with: window, duration: 0.3, options: .transitionCrossDissolve, animations: {
                window.rootViewController = navController
            }, completion: { _ in
                print("🔐 已成功导航到登录页面")
            })
        } else {
            print("🔐 无法获取窗口，尝试备用导航方案...")
            // 备用方案：使用 Router
            Router.shared.navigate(to: .intro, from: nil)
        }
    }
}

// MARK: - 扩展现有的 NetworkError 以支持错误代码 10001
extension NetworkError {
    /// 检查是否是认证失败错误（错误代码 10001）
    var isAuthenticationError: Bool {
        switch self {
        case .serverError(let code, _):
            return code == 10001
        default:
            return false
        }
    }
    
    /// 获取错误代码（如果有的话）
    var errorCode: Int? {
        switch self {
        case .serverError(let code, _):
            return code
        default:
            return nil
        }
    }
}
